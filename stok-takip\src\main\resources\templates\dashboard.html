<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Dashboard - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <!-- Dashboard Özet Kartları -->
        <div class="dashboard-cards">
            <div class="dashboard-card">
                <div class="dashboard-card-title">
                    <i class="fas fa-box me-2"></i>Toplam Ürün
                </div>
                <div class="dashboard-card-value" th:text="${totalProducts}">0</div>
                <a th:href="@{/products}" class="dashboard-card-link">
                    <i class="fas fa-arrow-right me-1"></i>Ürünleri Görüntüle
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-card-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Düşük Stok Ürünler
                </div>
                <div class="dashboard-card-value" th:text="${lowStockProducts.size()}">0</div>
                <a th:href="@{/products/low-stock}" class="dashboard-card-link">
                    <i class="fas fa-arrow-right me-1"></i>Düşük Stok Ürünleri Görüntüle
                </a>
            </div>

            <div class="dashboard-card">
                <div class="dashboard-card-title">
                    <i class="fas fa-bolt me-2"></i>Hızlı İşlemler
                </div>
                <div class="d-flex flex-column gap-2 mt-3">
                    <a th:href="@{/products/new}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Yeni Ürün Ekle
                    </a>
                    <a th:href="@{/stock-movements/new}" class="btn btn-success">
                        <i class="fas fa-exchange-alt me-1"></i>Stok Hareketi Ekle
                    </a>
                </div>
            </div>
        </div>

        <!-- Stok Grafiği -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Stok Durumu</h5>
            </div>
            <div class="card-body">
                <canvas id="stockChart" height="200"></canvas>
            </div>
        </div>

        <!-- Düşük Stok Ürünler ve Son Stok Hareketleri -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Düşük Stok Ürünler</h5>
                        <a th:href="@{/products/low-stock}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>Tümünü Gör
                        </a>
                    </div>
                    <div class="card-body">
                        <div th:if="${lowStockProducts.empty}" class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Düşük stok ürün bulunmamaktadır.
                        </div>
                        <div th:unless="${lowStockProducts.empty}" class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ürün Adı</th>
                                        <th>Stok</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="product : ${lowStockProducts}">
                                        <td>
                                            <span th:text="${product.name}">Ürün Adı</span>
                                            <small class="text-muted d-block" th:text="${product.code}">Kod</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger" th:text="${product.stockQuantity}">0</span>
                                        </td>
                                        <td>
                                            <a th:href="@{/stock-movements/new}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus me-1"></i>Stok Ekle
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Son Stok Hareketleri</h5>
                        <a th:href="@{/stock-movements}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>Tümünü Gör
                        </a>
                    </div>
                    <div class="card-body">
                        <div th:if="${recentMovements.empty}" class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Henüz stok hareketi bulunmamaktadır.
                        </div>
                        <div th:unless="${recentMovements.empty}" class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ürün</th>
                                        <th>Hareket</th>
                                        <th>Miktar</th>
                                        <th>Tarih</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="movement : ${recentMovements}">
                                        <td th:text="${movement.product.name}">Ürün Adı</td>
                                        <td>
                                            <span th:if="${movement.movementType.name() == 'IN'}" class="badge bg-success">
                                                <i class="fas fa-arrow-down me-1"></i>Giriş
                                            </span>
                                            <span th:if="${movement.movementType.name() == 'OUT'}" class="badge bg-danger">
                                                <i class="fas fa-arrow-up me-1"></i>Çıkış
                                            </span>
                                        </td>
                                        <td th:text="${movement.quantity}">0</td>
                                        <td th:text="${#temporals.format(movement.date, 'dd.MM.yyyy HH:mm')}">01.01.2023</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block layout:fragment="scripts">
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Chart initialization for dashboard
                const chartCanvas = document.getElementById('stockChart');
                if (chartCanvas && typeof Chart !== 'undefined') {
                    const ctx = chartCanvas.getContext('2d');

                    // Sample data - in a real app, this would come from the server
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Laptop', 'Akıllı Telefon', 'Süt', 'Tükenmez Kalem'],
                            datasets: [{
                                label: 'Stok Miktarı',
                                data: [5, 10, 50, 100],
                                backgroundColor: [
                                    'rgba(52, 152, 219, 0.7)',
                                    'rgba(46, 204, 113, 0.7)',
                                    'rgba(155, 89, 182, 0.7)',
                                    'rgba(241, 196, 15, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(52, 152, 219, 1)',
                                    'rgba(46, 204, 113, 1)',
                                    'rgba(155, 89, 182, 1)',
                                    'rgba(241, 196, 15, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            });
        </script>
    </th:block>
</body>
</html>
