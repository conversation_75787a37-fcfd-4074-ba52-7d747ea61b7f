<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Düşük Stok - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1>Düşük Stok Ürünler</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a th:href="@{/stock-movements/new}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-lg"></i> Stok Ekle
                </a>
            </div>
        </div>

        <!-- Filtre -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Filtrele</h5>
            </div>
            <div class="card-body">
                <form th:action="@{/products/low-stock}" method="get">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="category" class="form-label">Kategori</label>
                            <select class="form-select" id="category" name="categoryId">
                                <option value="">Tüm Kategoriler</option>
                                <option th:each="cat : ${categories}" th:value="${cat.id}" th:text="${cat.name}" 
                                        th:selected="${categoryId != null && categoryId == cat.id}">Kategori</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="threshold" class="form-label">Eşik Değeri</label>
                            <input type="number" class="form-control" id="threshold" name="threshold" min="1" 
                                   th:value="${threshold != null ? threshold : 10}">
                        </div>
                        <div class="col-md-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">Filtrele</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Ürün Kodu</th>
                        <th>Ürün Adı</th>
                        <th>Kategori</th>
                        <th>Mevcut Stok</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="product : ${products}">
                        <td th:text="${product.id}">1</td>
                        <td th:text="${product.code}">ABC123</td>
                        <td th:text="${product.name}">Ürün Adı</td>
                        <td th:text="${product.category != null ? product.category.name : '-'}">Kategori</td>
                        <td>
                            <span class="badge bg-danger" th:text="${product.stockQuantity}">0</span>
                        </td>
                        <td>
                            <a th:href="@{/stock-movements/new(productId=${product.id})}" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-lg"></i> Stok Ekle
                            </a>
                            <a th:href="@{/products/edit/{id}(id=${product.id})}" class="btn btn-sm btn-secondary">
                                <i class="bi bi-pencil"></i> Düzenle
                            </a>
                        </td>
                    </tr>
                    <tr th:if="${products.empty}">
                        <td colspan="6" class="text-center">Düşük stok ürün bulunmamaktadır.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
