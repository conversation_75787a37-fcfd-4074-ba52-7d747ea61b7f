/* Main Styles */
:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-color: #6c757d;
    --white-color: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 0.25rem;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header */
.navbar {
    background-color: var(--primary-color) !important;
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    color: var(--white-color) !important;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: 1.5rem;
}

.nav-link {
    color: var(--white-color) !important;
    text-decoration: none;
    transition: var(--transition);
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

/* Cards */
.card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background-color: var(--light-color);
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: bold;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-card-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--gray-color);
}

.dashboard-card-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.dashboard-card-link {
    margin-top: auto;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.dashboard-card-link:hover {
    color: var(--secondary-color);
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--white-color);
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--white-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: var(--white-color);
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    color: #495057;
    background-color: var(--white-color);
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Alerts */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius);
}

.bg-primary, .badge-primary {
    background-color: var(--primary-color) !important;
    color: var(--white-color);
}

.bg-success, .badge-success {
    background-color: var(--success-color) !important;
    color: var(--white-color);
}

.bg-danger, .badge-danger {
    background-color: var(--danger-color) !important;
    color: var(--white-color);
}

.bg-warning, .badge-warning {
    background-color: var(--warning-color) !important;
    color: var(--white-color);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 1.5rem 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .navbar-nav {
        margin-top: 1rem;
        flex-direction: column;
    }

    .nav-item {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}
