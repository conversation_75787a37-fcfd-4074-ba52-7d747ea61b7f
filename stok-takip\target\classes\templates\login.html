<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Stok Takip Sistemi</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo i {
            font-size: 3rem;
            color: #3498db;
        }

        .login-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-form .form-group {
            margin-bottom: 20px;
        }

        .login-form .form-control {
            height: 50px;
            border-radius: 5px;
            padding-left: 45px;
        }

        .login-form .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .login-form .input-icon {
            position: absolute;
            left: 15px;
            top: 17px;
            color: #3498db;
        }

        .login-btn {
            height: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 5px;
            background-color: #3498db;
            border-color: #3498db;
            transition: all 0.3s;
        }

        .login-btn:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-2px);
        }

        .login-footer {
            text-align: center;
            margin-top: 30px;
        }

        .login-footer a {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <i class="fas fa-boxes"></i>
        </div>

        <div class="login-title">
            <h1 class="h3 fw-bold">Stok Takip Sistemi</h1>
            <p class="text-muted">Hesabınıza giriş yapın</p>
        </div>

        <!-- Alerts -->
        <div th:if="${param.error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>Geçersiz kullanıcı adı veya şifre.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div th:if="${param.logout}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>Başarıyla çıkış yaptınız.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- Login Form -->
        <form class="login-form" th:action="@{/login}" method="post">
            <div class="form-group position-relative">
                <i class="fas fa-user input-icon"></i>
                <input type="text" class="form-control" id="username" name="username" placeholder="Kullanıcı Adı" required>
            </div>

            <div class="form-group position-relative">
                <i class="fas fa-lock input-icon"></i>
                <input type="password" class="form-control" id="password" name="password" placeholder="Şifre" required>
            </div>

            <button type="submit" class="btn btn-primary w-100 login-btn">
                <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
            </button>
        </form>

        <div class="login-footer">
            <p>Hesabınız yok mu? <a th:href="@{/register}">Kayıt Ol</a></p>
            <p class="text-muted mt-3">&copy; 2025 Stok Takip Sistemi</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
