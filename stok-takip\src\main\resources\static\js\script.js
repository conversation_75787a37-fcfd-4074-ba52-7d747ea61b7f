document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const menuToggle = document.getElementById('menu-toggle');
    const navbarNav = document.getElementById('navbar-nav');
    
    if (menuToggle && navbarNav) {
        menuToggle.addEventListener('click', function() {
            navbarNav.classList.toggle('show');
        });
    }
    
    // Flash messages auto-hide
    const alerts = document.querySelectorAll('.alert');
    if (alerts.length > 0) {
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });
    }
    
    // Table row highlighting
    const tableRows = document.querySelectorAll('.table tbody tr');
    if (tableRows.length > 0) {
        tableRows.forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('highlight');
            });
            
            row.addEventListener('mouseout', function() {
                this.classList.remove('highlight');
            });
        });
    }
    
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    if (forms.length > 0) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }
    
    // Stock quantity warning
    const stockQuantities = document.querySelectorAll('.stock-quantity');
    if (stockQuantities.length > 0) {
        stockQuantities.forEach(element => {
            const quantity = parseInt(element.textContent);
            if (quantity <= 10) {
                element.classList.add('text-danger');
                element.classList.add('fw-bold');
            } else if (quantity <= 20) {
                element.classList.add('text-warning');
            }
        });
    }
    
    // Date range picker initialization
    const dateRangePickers = document.querySelectorAll('.date-range-picker');
    if (dateRangePickers.length > 0 && typeof flatpickr !== 'undefined') {
        dateRangePickers.forEach(picker => {
            flatpickr(picker, {
                mode: "range",
                dateFormat: "Y-m-d"
            });
        });
    }
    
    // Chart initialization for dashboard
    const chartCanvas = document.getElementById('stockChart');
    if (chartCanvas && typeof Chart !== 'undefined') {
        const ctx = chartCanvas.getContext('2d');
        
        // Sample data - in a real app, this would come from the server
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Laptop', 'Akıllı Telefon', 'Süt', 'Tükenmez Kalem'],
                datasets: [{
                    label: 'Stok Miktarı',
                    data: [5, 10, 50, 100],
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.7)',
                        'rgba(46, 204, 113, 0.7)',
                        'rgba(155, 89, 182, 0.7)',
                        'rgba(241, 196, 15, 0.7)'
                    ],
                    borderColor: [
                        'rgba(52, 152, 219, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(241, 196, 15, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Print functionality
    const printButtons = document.querySelectorAll('.btn-print');
    if (printButtons.length > 0) {
        printButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    }
    
    // Confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete');
    if (deleteButtons.length > 0) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Bu öğeyi silmek istediğinizden emin misiniz?')) {
                    e.preventDefault();
                }
            });
        });
    }
});
