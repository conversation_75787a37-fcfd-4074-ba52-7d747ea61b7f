<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title th:text="${category.id == null ? '<PERSON><PERSON><PERSON>' : '<PERSON><PERSON><PERSON>'} + ' - Stok Takip Sistemi'"><PERSON><PERSON><PERSON>/Düzenle - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 th:text="${category.id == null ? '<PERSON><PERSON><PERSON>' : 'Kategori Düzenle'}">Kate<PERSON><PERSON>/<PERSON>ü<PERSON></h1>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <form th:action="@{${category.id == null ? '/categories' : '/categories/update'}}" th:object="${category}" method="post">
                            <input type="hidden" th:field="*{id}">

                            <div class="mb-3">
                                <label for="name" class="form-label">Kategori Adı</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                                <div class="text-danger" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/categories}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
