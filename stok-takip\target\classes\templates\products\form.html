<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title th:text="${product.id == null ? '<PERSON>rü<PERSON>' : '<PERSON><PERSON>ü<PERSON>'} + ' - Stok Takip Sistemi'"><PERSON>r<PERSON><PERSON>/Düzenle - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 th:text="${product.id == null ? 'Ürün <PERSON>kle' : 'Ürün Düzenle'}">Ür<PERSON><PERSON>/Düzenle</h1>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form th:action="@{${product.id == null ? '/products' : '/products/update'}}" th:object="${product}" method="post">
                            <input type="hidden" th:field="*{id}">

                            <div class="mb-3">
                                <label for="code" class="form-label">Ürün Kodu</label>
                                <input type="text" class="form-control" id="code" th:field="*{code}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('code')}" th:errors="*{code}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="name" class="form-label">Ürün Adı</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                                <div class="text-danger" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="category" class="form-label">Kategori</label>
                                <select class="form-select" id="category" th:field="*{category.id}" required>
                                    <option value="">Kategori Seçin</option>
                                    <option th:each="cat : ${categories}" th:value="${cat.id}" th:text="${cat.name}">Kategori</option>
                                </select>
                                <div class="text-danger" th:if="${#fields.hasErrors('category')}" th:errors="*{category}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="price" class="form-label">Fiyat</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" th:field="*{price}" step="0.01" min="0" required>
                                    <span class="input-group-text">₺</span>
                                </div>
                                <div class="text-danger" th:if="${#fields.hasErrors('price')}" th:errors="*{price}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="stockQuantity" class="form-label">Stok Miktarı</label>
                                <input type="number" class="form-control" id="stockQuantity" th:field="*{stockQuantity}" min="0" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('stockQuantity')}" th:errors="*{stockQuantity}"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/products}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
