<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kate<PERSON><PERSON>/Düzenle - Stok Takip Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Stok Takip Sistemi</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Kullanıcı
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Profil</a></li>
                            <li><a class="dropdown-item" href="#">Şifre Değiştir</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="login.html">Çıkış Yap</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.html">
                                <i class="bi bi-box-seam"></i> Ürünler
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="categories.html">
                                <i class="bi bi-tags"></i> Kategoriler
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="stock-movements.html">
                                <i class="bi bi-arrow-left-right"></i> Stok Hareketleri
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="low-stock.html">
                                <i class="bi bi-exclamation-triangle"></i> Düşük Stok
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.html">
                                <i class="bi bi-people"></i> Kullanıcı Yönetimi
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1>Kategori Ekle/Düzenle</h1>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <form action="categories.html" method="get">
                                    <input type="hidden" id="id" name="id" value="">
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Kategori Adı</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Açıklama</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="categories.html" class="btn btn-secondary">İptal</a>
                                        <button type="submit" class="btn btn-primary">Kaydet</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // URL'den id parametresini al
        const urlParams = new URLSearchParams(window.location.search);
        const categoryId = urlParams.get('id');
        
        // Eğer id parametresi varsa, form alanlarını doldur
        if (categoryId) {
            document.title = "Kategori Düzenle - Stok Takip Sistemi";
            document.querySelector('h1').textContent = "Kategori Düzenle";
            
            // Örnek veri (gerçek uygulamada API'den alınır)
            const categories = [
                { id: 1, name: "Elektronik", description: "Elektronik ürünler" },
                { id: 2, name: "Gıda", description: "Gıda ürünleri" },
                { id: 3, name: "Kırtasiye", description: "Kırtasiye ürünleri" }
            ];
            
            const category = categories.find(c => c.id == categoryId);
            
            if (category) {
                document.getElementById('id').value = category.id;
                document.getElementById('name').value = category.name;
                document.getElementById('description').value = category.description;
            }
        }
    </script>
</body>
</html>
