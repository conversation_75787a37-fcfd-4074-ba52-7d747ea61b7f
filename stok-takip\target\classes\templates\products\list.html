<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Ürünler - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a th:href="@{/products/new}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus-circle"></i> Yeni <PERSON><PERSON>
                </a>
            </div>
        </div>

        <!-- Arama Formu -->
        <div class="card mb-4">
            <div class="card-body">
                <form th:action="@{/products/search}" method="get" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="query" name="query" placeholder="Ürün adı ara...">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">Ara</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Ürün Kodu</th>
                        <th>Ürün Adı</th>
                        <th>Kategori</th>
                        <th>Fiyat</th>
                        <th>Stok</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="product : ${products}">
                        <td th:text="${product.id}">1</td>
                        <td th:text="${product.code}">ABC123</td>
                        <td th:text="${product.name}">Ürün Adı</td>
                        <td th:text="${product.category != null ? product.category.name : '-'}">Kategori</td>
                        <td th:text="${#numbers.formatDecimal(product.price, 1, 'POINT', 2, 'COMMA') + ' ₺'}">0.00 ₺</td>
                        <td>
                            <span th:class="${product.stockQuantity < 10 ? 'badge bg-danger' : 'badge bg-success'}"
                                  th:text="${product.stockQuantity}">0</span>
                        </td>
                        <td>
                            <a th:href="@{/products/edit/{id}(id=${product.id})}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a th:href="@{/products/delete/{id}(id=${product.id})}"
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Bu ürünü silmek istediğinize emin misiniz?')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                            <a th:href="@{/stock-movements/product/{id}(id=${product.id})}" class="btn btn-sm btn-info">
                                <i class="fas fa-exchange-alt"></i>
                            </a>
                        </td>
                    </tr>
                    <tr th:if="${products.empty}">
                        <td colspan="7" class="text-center">Henüz ürün bulunmamaktadır.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
