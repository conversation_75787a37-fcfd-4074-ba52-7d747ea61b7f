<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Stok Hareketleri - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1>Stok Hareketleri</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a th:href="@{/stock-movements/new}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus-circle"></i> Yeni Stok Hareketi
                </a>
            </div>
        </div>

        <!-- Filtre -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Filtrele</h5>
            </div>
            <div class="card-body">
                <form th:action="@{/stock-movements/filter}" method="get">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="product" class="form-label">Ürün</label>
                            <select class="form-select" id="product" name="productId">
                                <option value="">Tüm Ürünler</option>
                                <option th:each="prod : ${allProducts}" th:value="${prod.id}" th:text="${prod.name}"
                                        th:selected="${productId != null && productId == prod.id}">Ürün</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="movementType" class="form-label">Hareket Tipi</label>
                            <select class="form-select" id="movementType" name="movementType">
                                <option value="">Tümü</option>
                                <option th:each="type : ${T(com.stoktakip.model.StockMovement.MovementType).values()}"
                                        th:value="${type}" th:text="${type.displayName}"
                                        th:selected="${movementType != null && movementType == type}">Hareket Tipi</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="endDate" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                        </div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">Filtrele</button>
                        <a th:href="@{/stock-movements}" class="btn btn-secondary">Temizle</a>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tarih</th>
                        <th>Ürün</th>
                        <th>Hareket Tipi</th>
                        <th>Miktar</th>
                        <th>Kullanıcı</th>
                        <th>Not</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="movement : ${stockMovements}">
                        <td th:text="${movement.id}">1</td>
                        <td th:text="${#temporals.format(movement.date, 'dd.MM.yyyy HH:mm')}">01.01.2023 10:00</td>
                        <td th:text="${movement.product.name}">Ürün</td>
                        <td>
                            <span th:class="${movement.movementType.name() == 'IN' ? 'badge bg-success' : 'badge bg-danger'}"
                                  th:text="${movement.movementType.displayName}">Giriş/Çıkış</span>
                        </td>
                        <td th:text="${movement.quantity}">0</td>
                        <td th:text="${movement.user.username}">admin</td>
                        <td th:text="${movement.note}">Not</td>
                        <td>
                            <a th:href="@{/stock-movements/delete/{id}(id=${movement.id})}"
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Bu stok hareketini silmek istediğinize emin misiniz?')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </td>
                    </tr>
                    <tr th:if="${stockMovements.empty}">
                        <td colspan="8" class="text-center">Henüz stok hareketi bulunmamaktadır.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
